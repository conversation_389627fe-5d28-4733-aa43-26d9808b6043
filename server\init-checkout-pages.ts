import { storage } from './storage';
import { nanoid } from 'nanoid';

/**
 * Initialize default checkout pages
 */
export async function initializeDefaultCheckoutPages() {
  try {
    console.log('Initializing default checkout pages...');
    
    // Check if we already have checkout pages
    const existingPages = await storage.getCustomCheckoutPages();
    if (existingPages.length > 0) {
      console.log(`Found ${existingPages.length} existing checkout pages. Skipping initialization.`);
      return;
    }
    
    // Create trial checkout page
    const trialSlug = `default-trial-checkout-${nanoid(6)}`;
    const trialCheckoutPage = await storage.createCustomCheckoutPage({
      title: 'Default Trial Checkout',
      slug: trialSlug,
      productName: 'IPTV Trial Subscription',
      productDescription: 'Try our premium IPTV service for 24 hours with full access to all channels and features.',
      price: 4.99,
      imageUrl: '',
      paymentMethod: 'trial-paypal-button-embed',
      trialPaypalButtonId: 'trial-button-1',
      smtpProviderId: 'smtp-1',
      requireUsername: false,
      isTrialCheckout: true,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    console.log('Created trial checkout page:', trialCheckoutPage);
    
    // Create regular checkout page
    const regularSlug = `default-regular-checkout-${nanoid(6)}`;
    const regularCheckoutPage = await storage.createCustomCheckoutPage({
      title: 'Default Regular Checkout',
      slug: regularSlug,
      productName: 'IPTV Premium Subscription',
      productDescription: 'Get access to our premium IPTV service with over 10,000 channels, VOD, and more.',
      price: 19.99,
      imageUrl: '',
      paymentMethod: 'paypal-button-embed',
      paypalButtonId: 'button-1',
      smtpProviderId: 'smtp-1',
      requireUsername: false,
      isTrialCheckout: false,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    console.log('Created regular checkout page:', regularCheckoutPage);
    
    console.log('Default checkout pages initialized successfully!');
  } catch (error) {
    console.error('Error initializing default checkout pages:', error);
  }
}
