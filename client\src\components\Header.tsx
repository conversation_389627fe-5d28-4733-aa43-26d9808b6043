import React, { useState } from 'react';
import { <PERSON> } from 'wouter';
import { Menu, X } from 'lucide-react';

const Header: React.FC = () => {
  // Use the default primary color
  const primaryColor = '#0070BA';

  // Use the default business name
  const businessName = 'Invoice Generator';

  // Mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header
      className="border-b border-border sticky top-0 bg-background z-10"
    >
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <svg className="h-8" viewBox="0 0 124 33" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M46.1,11.2H38.9c-0.5,0-0.9,0.4-1,0.9l-2.8,18c-0.1,0.3,0.2,0.6,0.5,0.6h3.5c0.5,0,0.9-0.4,1-0.9l0.8-4.9 c0.1-0.5,0.5-0.9,1-0.9h2.3c4.7,0,7.3-2.3,8-6.8c0.3-2-0.1-3.5-1-4.6C50,12,48.3,11.2,46.1,11.2z M46.9,17.8 c-0.4,2.5-2.2,2.5-4,2.5h-1l0.7-4.6c0-0.3,0.3-0.5,0.6-0.5h0.5c1.2,0,2.4,0,3,0.7C47,16.3,47.1,16.9,46.9,17.8z" fill={primaryColor}/>
            <path d="M68.2,17.7h-3.5c-0.3,0-0.6,0.2-0.6,0.5l-0.2,1l-0.3-0.4c-0.8-1.2-2.7-1.6-4.5-1.6c-4.2,0-7.8,3.2-8.5,7.7 c-0.4,2.2,0.2,4.4,1.5,5.9c1.2,1.4,3,1.9,5,1.9c3.5,0,5.5-2.3,5.5-2.3l-0.2,1.1c-0.1,0.3,0.2,0.6,0.5,0.6h3.2c0.5,0,0.9-0.4,1-0.9 l1.8-11.6C68.7,18,68.5,17.7,68.2,17.7z M62.8,24.7c-0.4,2.2-2.1,3.7-4.4,3.7c-1.1,0-2-0.4-2.6-1.1c-0.6-0.7-0.8-1.7-0.6-2.8 c0.3-2.2,2.1-3.7,4.3-3.7c1.1,0,2,0.4,2.6,1.1C62.7,22.6,62.9,23.6,62.8,24.7z" fill={primaryColor}/>
            <path d="M90.3,17.7h-3.5c-0.3,0-0.6,0.2-0.8,0.4l-4.5,6.6l-1.9-6.3c-0.1-0.4-0.5-0.7-0.9-0.7h-3.4c-0.4,0-0.7,0.4-0.6,0.8l3.6,10.5 l-3.4,4.8c-0.3,0.4,0,0.9,0.4,0.9h3.5c0.3,0,0.6-0.2,0.8-0.4l10.8-15.6C90.9,18.3,90.7,17.7,90.3,17.7z" fill={primaryColor}/>
            <path d="M95.5,11.8l-3,19.1c-0.1,0.3,0.2,0.6,0.5,0.6h3c0.5,0,0.9-0.4,1-0.9l3-18.8c0.1-0.3-0.2-0.6-0.5-0.6h-3.3 C95.8,11.2,95.5,11.5,95.5,11.8z" fill={primaryColor}/>
            <path d="M16.7,11.2H9.5c-0.5,0-0.9,0.4-1,0.9l-2.8,18c-0.1,0.3,0.2,0.6,0.5,0.6h3.7c0.3,0,0.6-0.2,0.6-0.5l0.8-5.2 c0.1-0.5,0.5-0.9,1-0.9h2.3c4.7,0,7.3-2.3,8-6.8c0.3-2-0.1-3.5-1-4.6C20.5,12,18.8,11.2,16.7,11.2z M17.4,17.8 c-0.4,2.5-2.2,2.5-4,2.5h-1l0.7-4.6c0-0.3,0.3-0.5,0.6-0.5h0.5c1.2,0,2.4,0,3,0.7C17.5,16.3,17.6,16.9,17.4,17.8z" fill={primaryColor}/>
            <path d="M31.2,17.7h-3.5c-0.3,0-0.6,0.2-0.6,0.5l-0.2,1l-0.3-0.4c-0.8-1.2-2.7-1.6-4.5-1.6c-4.2,0-7.8,3.2-8.5,7.7 c-0.4,2.2,0.2,4.4,1.5,5.9c1.2,1.4,3,1.9,5,1.9c3.5,0,5.5-2.3,5.5-2.3l-0.2,1.1c-0.1,0.3,0.2,0.6,0.5,0.6h3.2c0.5,0,0.9-0.4,1-0.9 l1.8-11.6C31.7,18,31.5,17.7,31.2,17.7z M25.8,24.7c-0.4,2.2-2.1,3.7-4.4,3.7c-1.1,0-2-0.4-2.6-1.1c-0.6-0.7-0.8-1.7-0.6-2.8 c0.3-2.2,2.1-3.7,4.3-3.7c1.1,0,2,0.4,2.6,1.1C25.7,22.6,25.9,23.6,25.8,24.7z" fill={primaryColor}/>
          </svg>
          <span
            className="ml-2 font-medium text-lg"
            style={{ color: primaryColor }}
          >
            {businessName}
          </span>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center">
          <nav>
            <ul className="flex space-x-6">
              <li>
                <Link
                  href="/"
                  className="hover:text-primary/80 font-medium"
                  style={{ color: primaryColor }}
                >
                  Products
                </Link>
              </li>
              <li>
                <Link
                  href="/admin/login"
                  className="text-foreground hover:text-primary font-medium"
                >
                  Admin
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground hover:text-primary font-medium"
                >
                  Help
                </a>
              </li>
            </ul>
          </nav>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex items-center md:hidden">
          <button
            onClick={toggleMobileMenu}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            {mobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <nav className="px-4 py-3">
            <ul className="space-y-3">
              <li>
                <Link
                  href="/"
                  className="block py-2 hover:text-primary/80 font-medium"
                  style={{ color: primaryColor }}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Products
                </Link>
              </li>
              <li>
                <Link
                  href="/admin/login"
                  className="block py-2 text-foreground hover:text-primary font-medium"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Admin
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="block py-2 text-foreground hover:text-primary font-medium"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Help
                </a>
              </li>
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
