import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { getGeneralConfig, updateGeneralConfig, GeneralConfig } from '../general-config';

export const generalSettingsRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Define the validation schema for general settings
const generalSettingsSchema = z.object({
  siteName: z.string().min(1, "Site name is required"),
  siteDescription: z.string().optional(),
  logoUrl: z.string().optional(),
  faviconUrl: z.string().optional(),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  footerText: z.string().optional(),
  enableCheckout: z.boolean().default(true),
  enableCustomCheckout: z.boolean().default(true),
  enableTestMode: z.boolean().default(true),
  defaultTestCustomer: z.object({
    enabled: z.boolean().default(true),
    name: z.string().min(1, "Customer name is required"),
    email: z.string().email("Please enter a valid email")
  }),
  emailDomainRestriction: z.object({
    enabled: z.boolean().default(true),
    allowedDomains: z.string().optional()
  })
});

// Get general settings
generalSettingsRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    res.json(settings);
  } catch (error) {
    console.error('Error fetching general settings:', error);
    res.status(500).json({ message: 'Failed to fetch general settings' });
  }
});

// Update general settings
generalSettingsRouter.put('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Validate the request body
    const validatedData = generalSettingsSchema.parse(req.body);
    
    // Update the settings
    const updatedSettings = updateGeneralConfig(validatedData);
    
    res.json(updatedSettings);
  } catch (error) {
    console.error('Error updating general settings:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to update general settings' });
  }
});

// Get allowed email domains
generalSettingsRouter.get('/allowed-email-domains', async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    const { enabled, allowedDomains } = settings.emailDomainRestriction;
    
    if (!enabled || !allowedDomains) {
      return res.json({ enabled, domains: [] });
    }
    
    const domains = allowedDomains
      .split(',')
      .map(domain => domain.trim())
      .filter(domain => domain.length > 0);
    
    res.json({ enabled, domains });
  } catch (error) {
    console.error('Error fetching allowed email domains:', error);
    res.status(500).json({ message: 'Failed to fetch allowed email domains' });
  }
});

// Get default test customer
generalSettingsRouter.get('/default-test-customer', async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    const { defaultTestCustomer } = settings;
    
    res.json(defaultTestCustomer);
  } catch (error) {
    console.error('Error fetching default test customer:', error);
    res.status(500).json({ message: 'Failed to fetch default test customer' });
  }
});
