import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import ImageUploader from '@/components/admin/ImageUploader';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SimpleRichTextEditor from '@/components/admin/SimpleRichTextEditor';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Copy, ExternalLink, Eye, Link, Plus, Share2, Trash, Image as ImageIcon, AlertTriangle } from 'lucide-react';

// Form schema for custom checkout page
const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  price: z.coerce.number().positive('Price must be positive'),
  imageUrl: z.string().optional(),
  paymentMethod: z.enum(['paypal', 'custom-link', 'paypal-button-embed', 'trial-custom-link', 'trial-paypal-button-embed']),
  customPaymentLinkId: z.string().optional(),
  paypalButtonId: z.string().optional(),
  trialCustomPaymentLinkId: z.string().optional(),
  trialPaypalButtonId: z.string().optional(),
  confirmationMessage: z.string().optional(),
  smtpProviderId: z.string().optional(),
  requireAllowedEmail: z.boolean().default(false),
  isTrialCheckout: z.boolean().default(false),
  expiresAt: z.date().optional(),

  active: z.boolean().default(true)
});

type FormValues = z.infer<typeof formSchema>;

export default function CustomCheckoutPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPage, setSelectedPage] = useState<any>(null);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  // Query to fetch custom checkout pages
  const { data: pages, isLoading } = useQuery({
    queryKey: ['/api/custom-checkout'],
    queryFn: () => apiRequest('/api/custom-checkout', 'GET')
  });

  // Query to fetch payment config (for payment methods)
  const { data: paymentConfig } = useQuery({
    queryKey: ['/api/admin/payment-config'],
    queryFn: () => apiRequest('/api/admin/payment-config', 'GET')
  });

  // Query to fetch email config (for SMTP providers)
  const { data: emailConfig } = useQuery({
    queryKey: ['/api/admin/email-config'],
    queryFn: () => apiRequest('/api/admin/email-config', 'GET')
  });



  // Form for creating/editing custom checkout pages
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      slug: '',
      productName: '',
      productDescription: '',
      price: 0,
      imageUrl: '',
      paymentMethod: 'paypal',
      customPaymentLinkId: '',
      paypalButtonId: '',
      trialCustomPaymentLinkId: '',
      trialPaypalButtonId: '',
      confirmationMessage: '<div class="space-y-3"><p><strong>🛒 Ready to complete your purchase?</strong></p><p>✅ <strong>What you\'re getting:</strong></p><ul class="list-disc list-inside space-y-1"><li>Instant access to your product</li><li>Full customer support</li><li>Secure payment processing</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Your payment will be processed securely</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
      smtpProviderId: 'default',
      requireAllowedEmail: false,
      isTrialCheckout: false,

      active: true
    }
  });

  // Get available payment methods
  const paypalProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal');
  const customLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'custom-link');
  const paypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal-button-embed');
  const trialCustomLinkProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-custom-link');
  const trialPaypalButtonEmbedProvider = paymentConfig?.providers?.find((p: any) => p.id === 'trial-paypal-button-embed');

  const customLinks = customLinkProvider?.config?.links || [];
  const paypalButtons = paypalButtonEmbedProvider?.config?.buttons || [];
  const trialCustomLinks = trialCustomLinkProvider?.config?.links || [];
  const trialPaypalButtons = trialPaypalButtonEmbedProvider?.config?.buttons || [];

  // Get available SMTP providers
  const smtpProviders = emailConfig?.providers?.filter((p: any) => p.active) || [];

  // Mutation to create a new custom checkout page
  const createMutation = useMutation({
    mutationFn: (data: FormValues) => {
      console.log('Sending API request with data:', data);
      return apiRequest('/api/custom-checkout', 'POST', data);
    },
    onSuccess: () => {
      console.log('Custom checkout page created successfully');
      queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout'] });
      setIsCreateDialogOpen(false);
      form.reset();
      toast({
        title: 'Success',
        description: 'Custom checkout page created successfully',
      });
    },
    onError: (error: any) => {
      console.error('Error creating custom checkout page:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create custom checkout page',
        variant: 'destructive',
      });
    }
  });

  // Mutation to update a custom checkout page
  const updateMutation = useMutation({
    mutationFn: (data: { id: number, values: FormValues }) =>
      apiRequest(`/api/custom-checkout/${data.id}`, 'PUT', data.values),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout'] });
      setIsEditDialogOpen(false);
      setSelectedPage(null);
      toast({
        title: 'Success',
        description: 'Custom checkout page updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update custom checkout page',
        variant: 'destructive',
      });
    }
  });

  // Mutation to delete a custom checkout page
  const deleteMutation = useMutation({
    mutationFn: (id: number) => apiRequest(`/api/custom-checkout/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/custom-checkout'] });
      setIsDeleteDialogOpen(false);
      setSelectedPage(null);
      toast({
        title: 'Success',
        description: 'Custom checkout page deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete custom checkout page',
        variant: 'destructive',
      });
    }
  });

  // Handle form submission for creating a new page
  const onCreateSubmit = (values: FormValues) => {
    console.log('Creating custom checkout page with values:', values);
    createMutation.mutate(values);
  };

  // Handle form submission for updating a page
  const onEditSubmit = (values: FormValues) => {
    if (selectedPage) {
      updateMutation.mutate({ id: selectedPage.id, values });
    }
  };

  // Open edit dialog and populate form with selected page data
  const handleEditPage = (page: any) => {
    setSelectedPage(page);

    // Convert expiresAt string to Date object if it exists
    const formValues = {
      ...page,
      expiresAt: page.expiresAt ? new Date(page.expiresAt) : undefined
    };

    form.reset(formValues);
    setIsEditDialogOpen(true);
  };

  // Open delete confirmation dialog
  const handleDeletePage = (page: any) => {
    setSelectedPage(page);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDelete = () => {
    if (selectedPage) {
      deleteMutation.mutate(selectedPage.id);
    }
  };

  // Open share dialog
  const handleSharePage = (page: any) => {
    const baseUrl = window.location.origin;
    const shareUrl = `${baseUrl}/checkout/${page.slug}`;
    setShareUrl(shareUrl);
    setIsShareDialogOpen(true);
  };

  // Copy share URL to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: 'Copied!',
      description: 'Link copied to clipboard',
    });
  };

  // Reset form when opening create dialog
  const handleOpenCreateDialog = () => {
    form.reset({
      title: '',
      slug: '',
      productName: '',
      productDescription: '',
      price: 0,
      imageUrl: '',
      paymentMethod: 'paypal',
      customPaymentLinkId: '',
      paypalButtonId: '',
      trialCustomPaymentLinkId: '',
      trialPaypalButtonId: '',
      smtpProviderId: 'default',
      requireAllowedEmail: false,
      isTrialCheckout: false,

      active: true
    });
    setIsCreateDialogOpen(true);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Custom Checkout Pages</h1>
          <p className="text-muted-foreground">Create and manage custom checkout pages for your products</p>
        </div>
        <Button onClick={() => setLocation('/admin/custom-checkout/create')}>
          <Plus className="mr-2 h-4 w-4" /> Create New Page
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        </div>
      ) : pages && pages.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pages.map((page: any) => (
            <Card key={page.id} className="overflow-hidden">
              {page.imageUrl && (
                <div className="relative w-full h-40 overflow-hidden">
                  <img
                    src={page.imageUrl}
                    alt={page.productName}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{page.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {formatCurrency(page.price)}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge variant={page.active ? "default" : "outline"}>
                      {page.active ? "Active" : "Inactive"}
                    </Badge>
                    {page.isTrialCheckout && (
                      <Badge variant="secondary">Trial</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium">Product</h4>
                    <p className="text-sm text-muted-foreground">{page.productName}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Payment Method</h4>
                    <p className="text-sm text-muted-foreground capitalize">{page.paymentMethod}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium">Stats</h4>
                    <div className="flex space-x-4 text-sm text-muted-foreground">
                      <span>{page.views} views</span>
                      <span>{page.conversions} conversions</span>
                      <span>{page.conversions > 0 && page.views > 0 ?
                        `${Math.round((page.conversions / page.views) * 100)}% conversion rate` :
                        '0% conversion rate'}</span>
                    </div>
                  </div>
                  {page.expiresAt && (
                    <div>
                      <h4 className="text-sm font-medium">Expires</h4>
                      <p className="text-sm text-muted-foreground">{formatDate(page.expiresAt)}</p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between pt-3 border-t">
                <Button variant="outline" size="sm" onClick={() => handleSharePage(page)}>
                  <Share2 className="h-4 w-4 mr-2" /> Share
                </Button>
                <div className="space-x-2">
                  <Button variant="outline" size="sm" onClick={() => handleEditPage(page)}>
                    Edit
                  </Button>
                  <Button variant="destructive" size="sm" onClick={() => handleDeletePage(page)}>
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
            <Link className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-lg font-semibold">No custom checkout pages yet</h3>
          <p className="mb-6 text-sm text-muted-foreground">
            Create your first custom checkout page to start selling products with a unique URL.
          </p>
          <Button onClick={() => setLocation('/admin/custom-checkout/create')}>
            <Plus className="mr-2 h-4 w-4" /> Create New Page
          </Button>
        </Card>
      )}

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Custom Checkout Page</DialogTitle>
            <DialogDescription>
              Create a new checkout page with custom product details and payment method.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onCreateSubmit)} className="space-y-6">
              <Tabs defaultValue="basic">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="product">Product Details</TabsTrigger>
                  <TabsTrigger value="payment">Payment Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Summer Sale Offer" {...field} />
                        </FormControl>
                        <FormDescription>
                          A title for your checkout page (not visible to customers)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Slug (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="summer-sale" {...field} />
                        </FormControl>
                        <FormDescription>
                          Custom URL path for this checkout page. Leave blank to generate automatically.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiresAt"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Expiration Date (Optional)</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={`w-full justify-start text-left font-normal ${
                                  !field.value && "text-muted-foreground"
                                }`}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : "Select expiration date"}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                              disabled={(date) => date < new Date()}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          The checkout page will become inactive after this date
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />



                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Status</FormLabel>
                          <FormDescription>
                            Make this checkout page available to customers
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requireAllowedEmail"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Require Allowed Email</FormLabel>
                          <FormDescription>
                            Require customers to enter a valid email from your allowed list
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isTrialCheckout"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Trial Checkout</FormLabel>
                          <FormDescription>
                            Mark this as a trial subscription checkout page
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="product" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Premium WordPress Theme" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the product you're selling
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="productDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Description</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={200}
                            placeholder="A professionally designed WordPress theme with premium features..."
                            showPreview={true}
                          />
                        </FormControl>
                        <FormDescription>
                          Describe the product in detail. You can use formatting to make it more attractive.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            placeholder="49.99"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The price of the product in USD
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Image (Optional)</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <ImageUploader
                              initialUrl={field.value}
                              onImageUploaded={(url) => field.onChange(url)}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Upload an image for your product
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="payment" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Regular Payment Methods</SelectLabel>
                              {paypalProvider && (
                                <SelectItem value="paypal">
                                  PayPal Invoice {!paypalProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {customLinkProvider && (
                                <SelectItem value="custom-link">
                                  Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="paypal-button-embed">
                                  PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>Trial Payment Methods</SelectLabel>
                              {customLinkProvider && (
                                <SelectItem value="trial-custom-link">
                                  Trial Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="trial-paypal-button-embed">
                                  Trial PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose how customers will pay for this product
                        </FormDescription>
                        {field.value === 'paypal' && paypalProvider && !paypalProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'custom-link' && customLinkProvider && !customLinkProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Custom Payment Link is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'paypal-button-embed' && paypalButtonEmbedProvider && !paypalButtonEmbedProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal Button Embed is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('paymentMethod') === 'custom-link' && customLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="customPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {customLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'paypal-button-embed' && paypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="paypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-custom-link' && trialCustomLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialCustomPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialCustomLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-paypal-button-embed' && trialPaypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialPaypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialPaypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Email Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure which SMTP provider to use for sending emails from this checkout page
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="smtpProviderId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SMTP Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SMTP provider (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="default">Use default SMTP provider</SelectItem>
                            {smtpProviders.map((provider: any) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name} {provider.isDefault && "(Default)"} {provider.isBackup && "(Backup)"}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose a specific SMTP provider to use for sending emails from this checkout page.
                          If not selected, the default SMTP provider will be used.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <Button
                  type="submit"
                  disabled={createMutation.isPending}
                >
                  {createMutation.isPending ? 'Creating...' : 'Create Checkout Page'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Custom Checkout Page</DialogTitle>
            <DialogDescription>
              Update the details of your custom checkout page.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onEditSubmit)} className="space-y-6">
              {/* Same form fields as create dialog */}
              <Tabs defaultValue="basic">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="product">Product Details</TabsTrigger>
                  <TabsTrigger value="payment">Payment Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Summer Sale Offer" {...field} />
                        </FormControl>
                        <FormDescription>
                          A title for your checkout page (not visible to customers)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Slug</FormLabel>
                        <FormControl>
                          <Input placeholder="summer-sale" {...field} />
                        </FormControl>
                        <FormDescription>
                          Custom URL path for this checkout page
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmationMessage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmation Message</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={200}
                            placeholder="Enter a message to show in the confirmation popup before purchase"
                            showPreview={true}
                          />
                        </FormControl>
                        <FormDescription>
                          This message will be shown to the customer in a confirmation dialog before they complete their purchase.
                          You can use formatting to make it more attractive.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiresAt"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Expiration Date (Optional)</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={`w-full justify-start text-left font-normal ${
                                  !field.value && "text-muted-foreground"
                                }`}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : "Select expiration date"}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                              disabled={(date) => date < new Date()}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          The checkout page will become inactive after this date
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Status</FormLabel>
                          <FormDescription>
                            Make this checkout page available to customers
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requireAllowedEmail"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Require Allowed Email</FormLabel>
                          <FormDescription>
                            Require customers to enter a valid email from your allowed list
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isTrialCheckout"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Trial Checkout</FormLabel>
                          <FormDescription>
                            Mark this as a trial subscription checkout page
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="product" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Premium WordPress Theme" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the product you're selling
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="productDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Description</FormLabel>
                        <FormControl>
                          <SimpleRichTextEditor
                            initialValue={field.value}
                            onChange={field.onChange}
                            height={200}
                            placeholder="A professionally designed WordPress theme with premium features..."
                            showPreview={true}
                          />
                        </FormControl>
                        <FormDescription>
                          Describe the product in detail. You can use formatting to make it more attractive.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            placeholder="49.99"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The price of the product in USD
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Image (Optional)</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <ImageUploader
                              initialUrl={field.value}
                              onImageUploaded={(url) => field.onChange(url)}
                            />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Upload an image for your product
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="payment" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Regular Payment Methods</SelectLabel>
                              {paypalProvider && (
                                <SelectItem value="paypal">
                                  PayPal Invoice {!paypalProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {customLinkProvider && (
                                <SelectItem value="custom-link">
                                  Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="paypal-button-embed">
                                  PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>Trial Payment Methods</SelectLabel>
                              {customLinkProvider && (
                                <SelectItem value="trial-custom-link">
                                  Trial Custom Payment Link {!customLinkProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                              {paypalButtonEmbedProvider && (
                                <SelectItem value="trial-paypal-button-embed">
                                  Trial PayPal Button Embed {!paypalButtonEmbedProvider.active && " (Inactive)"}
                                </SelectItem>
                              )}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose how customers will pay for this product
                        </FormDescription>
                        {field.value === 'paypal' && paypalProvider && !paypalProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'custom-link' && customLinkProvider && !customLinkProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Custom Payment Link is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        {field.value === 'paypal-button-embed' && paypalButtonEmbedProvider && !paypalButtonEmbedProvider.active && (
                          <div className="mt-2 text-amber-600 text-sm flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>PayPal Button Embed is currently inactive. Activate it in Payment Settings.</span>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('paymentMethod') === 'custom-link' && customLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="customPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {customLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'paypal-button-embed' && paypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="paypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-custom-link' && trialCustomLinks.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialCustomPaymentLinkId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial Custom Payment Link</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial payment link" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialCustomLinks.map((link: any) => (
                                <SelectItem key={link.id} value={link.id}>
                                  {link.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial payment link to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch('paymentMethod') === 'trial-paypal-button-embed' && trialPaypalButtons.length > 0 && (
                    <FormField
                      control={form.control}
                      name="trialPaypalButtonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trial PayPal Button</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trial PayPal button" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {trialPaypalButtons.map((button: any) => (
                                <SelectItem key={button.id} value={button.id}>
                                  {button.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a specific trial PayPal button to use (optional)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <Separator className="my-4" />

                  <div className="mb-2">
                    <h3 className="text-lg font-medium">Email Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure which SMTP provider to use for sending emails from this checkout page
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="smtpProviderId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SMTP Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SMTP provider (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="default">Use default SMTP provider</SelectItem>
                            {smtpProviders.map((provider: any) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name} {provider.isDefault && "(Default)"} {provider.isBackup && "(Backup)"}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose a specific SMTP provider to use for sending emails from this checkout page.
                          If not selected, the default SMTP provider will be used.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <Button
                  type="submit"
                  disabled={updateMutation.isPending}
                >
                  {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the custom checkout page "{selectedPage?.title}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Share Dialog */}
      <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share Checkout Page</DialogTitle>
            <DialogDescription>
              Copy this link to share your custom checkout page with customers.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2">
            <Input value={shareUrl} readOnly />
            <Button size="icon" variant="outline" onClick={copyToClipboard}>
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          <DialogFooter>
            <Button asChild>
              <a href={shareUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="mr-2 h-4 w-4" /> Open Page
              </a>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
