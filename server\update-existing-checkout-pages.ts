import { storage } from './storage';

/**
 * Update existing checkout pages to add confirmation messages
 */
export async function updateExistingCheckoutPages() {
  try {
    console.log('Updating existing checkout pages with confirmation messages...');

    // Get all existing checkout pages
    const existingPages = await storage.getCustomCheckoutPages();

    for (const page of existingPages) {
      // Skip if page already has a confirmation message
      if (page.confirmationMessage && page.confirmationMessage.trim() !== '') {
        console.log(`Page "${page.title}" already has a confirmation message. Skipping.`);
        continue;
      }

      let confirmationMessage = '';

      if (page.isTrialCheckout) {
        // Trial checkout confirmation message
        confirmationMessage = '<div class="space-y-3"><p><strong>🎯 You are about to start your 24-hour trial!</strong></p><p>✅ <strong>What you get:</strong></p><ul class="list-disc list-inside space-y-1"><li>Full access to 10,000+ channels</li><li>Premium VOD content</li><li>HD/4K streaming quality</li><li>24/7 customer support</li></ul><p class="text-sm text-muted-foreground mt-3">💡 <em>Your trial will begin immediately after payment confirmation.</em></p></div>';
      } else {
        // Regular checkout confirmation message
        confirmationMessage = '<div class="space-y-3"><p><strong>🚀 Ready to upgrade to Premium?</strong></p><p>✅ <strong>Your Premium subscription includes:</strong></p><ul class="list-disc list-inside space-y-1"><li>10,000+ live TV channels</li><li>50,000+ movies & TV shows</li><li>4K Ultra HD streaming</li><li>Multi-device support</li><li>Premium sports packages</li><li>24/7 priority support</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Secure payment processing via PayPal</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>';
      }

      // Update the page with confirmation message
      await storage.updateCustomCheckoutPage(page.id, {
        confirmationMessage,
        updatedAt: new Date().toISOString()
      });

      console.log(`Updated page "${page.title}" with confirmation message.`);
    }

    console.log('Finished updating existing checkout pages!');
  } catch (error) {
    console.error('Error updating existing checkout pages:', error);
  }
}

// Run the update immediately
updateExistingCheckoutPages();
