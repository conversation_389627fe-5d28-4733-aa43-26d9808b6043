import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Product } from '@shared/schema';
import ProductGrid from '@/components/ProductGrid';
import CheckoutForm from '@/components/CheckoutForm';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Home: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showCheckout, setShowCheckout] = useState(false);
  
  // Fetch products
  const { data: products, isLoading } = useQuery<Product[]>({
    queryKey: ['/api/products'],
  });
  
  // Handle product selection
  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setShowCheckout(true);
    window.scrollTo(0, 0);
  };
  
  // Handle back to products
  const handleBackToProducts = () => {
    setShowCheckout(false);
    setSelectedProduct(null);
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow py-12 px-4">
        {showCheckout && selectedProduct ? (
          <CheckoutForm 
            product={selectedProduct}
            onBack={handleBackToProducts}
          />
        ) : (
          <ProductGrid 
            products={products || []}
            onSelectProduct={handleProductSelect}
            isLoading={isLoading}
          />
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default Home;
