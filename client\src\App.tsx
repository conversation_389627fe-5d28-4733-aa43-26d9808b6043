import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ErrorDialogProvider } from "@/hooks/use-error-dialog";
import { ConfirmationDialogProvider } from "@/hooks/use-confirmation-dialog";
import { SystemMessagesProvider } from "@/hooks/use-system-messages";

import NotFound from "@/pages/not-found";
import Home from "@/pages/Home";
import Checkout from "@/pages/Checkout";
import CustomCheckout from "@/pages/checkout/[slug]";
import PaymentSuccess from "@/pages/payment-success";
import PaymentCancel from "@/pages/payment-cancel";
import AdminLogin from "@/pages/AdminLogin";
import AdminDashboard from "@/pages/admin/Dashboard";
import AdminProducts from "@/pages/admin/Products";

import AdminSettings from "@/pages/admin/Settings";
import AdminEmailSettings from "@/pages/admin/EmailSettings";
import AdminPaymentSettings from "@/pages/admin/PaymentSettings";
import AdminGeneralSettings from "@/pages/admin/GeneralSettings";
import AdminCustomCheckout from "@/pages/admin/CustomCheckout";
import AdminAllowedEmails from "@/pages/admin/AllowedEmails";
import AdminOrders from "@/pages/admin/Orders";
import AdminTrialOrders from "@/pages/admin/TrialOrders";
import SystemUpdates from "@/pages/admin/SystemUpdates";
import SystemMessages from "@/pages/admin/SystemMessages";
import EmailTemplates from "@/pages/admin/EmailTemplates";

import ForgotPassword from "@/pages/admin/ForgotPassword";
import ResetPassword from "@/pages/admin/ResetPassword";
import AccountSettings from "@/pages/admin/AccountSettings";

import { AdminRoute } from "@/components/admin/AdminRoute";

function Router() {
  return (
    <Switch>
      {/* Client routes */}
      <Route path="/" component={Home} />
      <Route path="/checkout/:slug" component={CustomCheckout} />
      <Route path="/checkout/product/:id" component={Checkout} />
      <Route path="/payment-success" component={PaymentSuccess} />
      <Route path="/payment-cancel" component={PaymentCancel} />

      {/* Admin routes */}
      <Route path="/admin/login" component={AdminLogin} />
      <Route path="/admin/forgot-password" component={ForgotPassword} />
      <Route path="/admin/reset-password" component={ResetPassword} />
      <AdminRoute path="/admin/dashboard" component={AdminDashboard} />
      <AdminRoute path="/admin/products" component={AdminProducts} />

      <AdminRoute path="/admin/settings" component={AdminSettings} />
      <AdminRoute path="/admin/email-settings" component={AdminEmailSettings} />
      <AdminRoute path="/admin/payment-settings" component={AdminPaymentSettings} />
      <AdminRoute path="/admin/general-settings" component={AdminGeneralSettings} />
      <AdminRoute path="/admin/custom-checkout" component={AdminCustomCheckout} />
      <AdminRoute path="/admin/allowed-emails" component={AdminAllowedEmails} />
      <AdminRoute path="/admin/orders" component={AdminOrders} />
      <AdminRoute path="/admin/trial-orders" component={AdminTrialOrders} />
      <AdminRoute path="/admin/account-settings" component={AccountSettings} />
      <AdminRoute path="/admin/system-updates" component={SystemUpdates} />
      <AdminRoute path="/admin/system-messages" component={SystemMessages} />
      <AdminRoute path="/admin/email-templates" component={EmailTemplates} />


      {/* 404 catch-all */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <SystemMessagesProvider>
          <ErrorDialogProvider>
            <ConfirmationDialogProvider>
              <Toaster />
              <Router />
            </ConfirmationDialogProvider>
          </ErrorDialogProvider>
        </SystemMessagesProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
